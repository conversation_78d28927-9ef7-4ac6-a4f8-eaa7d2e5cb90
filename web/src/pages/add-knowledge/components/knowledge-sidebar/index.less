.sidebarWrapper {
  max-width: 288px;
  padding: 32px 24px 24px 24px;
  flex-direction: column;

  .sidebarTop {
    text-align: center;
    .knowledgeLogo {
    }
    .knowledgeTitle {
      font-size: 16px;
      line-height: 24px;
      font-weight: @fontWeight700;
      margin-bottom: 6px;
    }
    .knowledgeDescription {
      font-size: 12px;
      font-weight: @fontWeight600;
      color: @gray8;
      margin: 0;
    }
    padding-bottom: 20px;
  }
  .divider {
    height: 2px;
    background-image: linear-gradient(
      to right,
      @gray11 0%,
      @gray11 50%,
      transparent 50%
    );
    background-size: 10px 2px;
    background-repeat: repeat-x;
  }

  .menuWrapper {
    padding-top: 10px;

    .menu {
      border: none;
      font-size: @fontSize16;
      font-weight: @fontWeight600;
      :global(.ant-menu-item) {
        display: flex;
        align-items: center;
      }
    }

    .defaultWidth {
      width: 240px;
    }

    .minWidth {
      width: 50px;
    }

    .menuText {
      color: @gray3;
      font-size: @fontSize14;
      font-weight: @fontWeight700;
    }
  }
}
