---
sidebar_position: 3
slug: /retrieval_component
---

# Retrieval component

A component that retrieves information from specified datasets.

## Scenarios

A **Retrieval** component is essential in most RAG scenarios, where information is extracted from designated knowledge bases before being sent to the LLM for content generation. As of v0.20.3, a **Retrieval** component can operate either as a workflow component or as a tool of an **Agent**, enabling the Agent to control its invocation and search queries.

## Configurations

Click on a **Retrieval** component to open its configuration window.

### Query variables

*Mandatory*

Select the query source for retrieval.

The **Retrieval** component relies on query variables to specify its data inputs (queries). All global variables defined before the **Retrieval** component are available in the dropdown list.  

### Knowledge bases 

Select the knowledge base(s) to retrieve data from.

- If no knowledge base is selected, meaning conversations with the agent will not be based on any knowledge base, ensure that the **Empty response** field is left blank to avoid an error.
- If you select multiple knowledge bases, you must ensure that the knowledge bases (datasets) you select use the same embedding model; otherwise, an error message would occur.

### Similarity threshold

RAGFlow employs a combination of weighted keyword similarity and weighted vector cosine similarity during retrieval. This parameter sets the threshold for similarities between the user query and chunks stored in the datasets. Any chunk with a similarity score below this threshold will be excluded from the results.

Defaults to 0.2.

### Keyword similarity weight

This parameter sets the weight of keyword similarity in the combined similarity score. The total of the two weights must equal 1.0. Its default value is 0.7, which means the weight of vector similarity in the combined search is 1 - 0.7 = 0.3.

### Top N

This parameter selects the "Top N" chunks from retrieved ones and feed them to the LLM.

Defaults to 8.


### Rerank model

*Optional*

If a rerank model is selected, a combination of weighted keyword similarity and weighted reranking score will be used for retrieval.

:::caution WARNING
Using a rerank model will *significantly* increase the system's response time.
:::

### Empty response

- Set this as a response if no results are retrieved from the knowledge base(s) for your query, or 
- Leave this field blank to allow the chat model to improvise when nothing is found.

:::caution WARNING
If you do not specify a knowledge base, you must leave this field blank; otherwise, an error would occur.
:::

### Cross-language search

Select one or more languages for cross‑language search. If no language is selected, the system searches with the original query.

### Use knowledge graph

Whether to use knowledge graph(s) in the specified knowledge base(s) during retrieval for multi-hop question answering. When enabled, this would involve iterative searches across entity, relationship, and community report chunks, greatly increasing retrieval time.

### Output

The global variable name for the output of the **Retrieval** component, which can be referenced by other components in the workflow.
